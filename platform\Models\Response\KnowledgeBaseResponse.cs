using Amazon.DynamoDBv2.DataModel;
using platform.Models.Enum;
using shared.Converters;
using shared.Models.Response;
using System.Text.Json.Serialization;

namespace platform.Models.Response
{
    public class KnowledgeBaseResponse : BaseModelResponse
    {
        public string KbId { get; set; } = string.Empty;

        [DynamoDBProperty(typeof(DynamoEnumStringConverter<KnowledgeBaseStatus>))]
        [JsonConverter(typeof(JsonEnumStringConverter<KnowledgeBaseStatus>))]
        public KnowledgeBaseStatus Status { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Tag { get; set; } = string.Empty;
    }
}
