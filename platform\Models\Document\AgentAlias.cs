using Amazon.DynamoDBv2.DataModel;
using shared.Models.Document;

namespace platform.Models.Document
{
    [DynamoDBTable(nameof(AgentAlias))]
    public class AgentAlias : BaseModel
    {
        public const string AccountIdAliasIndex = "AccountId-Alias-index";

        [DynamoDBHashKey]
        public string AgentId { get; set; } = string.Empty;
        [DynamoDBRangeKey]
        [DynamoDBGlobalSecondaryIndexRangeKey(AccountIdAliasIndex)]
        public string Alias { get; set; } = string.Empty;
        [DynamoDBGlobalSecondaryIndexHashKey(AccountIdAliasIndex)]
        public string AccountId {  get; set; } = string.Empty;
        public string AgentTag { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
    }
}
