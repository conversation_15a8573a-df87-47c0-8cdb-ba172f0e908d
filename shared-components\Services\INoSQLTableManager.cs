using shared.Models.Document;

namespace shared.Services
{
    /// <summary>
    /// Interface for NoSQL table management operations.
    /// Handles table creation, validation, and deletion separately from data operations.
    /// </summary>
    /// <typeparam name="T">Model type that extends NoSQLModel</typeparam>
    public interface INoSQLTableManager<T> where T : NoSQLModel
    {
        /// <summary>
        /// Creates a table for the model type with all primary keys and secondary indexes.
        /// Automatically detects and creates GSI and LSI indexes based on model attributes.
        /// </summary>
        /// <returns>True if table was created successfully or already exists</returns>
        Task<bool> CreateTableAsync();

        /// <summary>
        /// Deletes a table for the model type.
        /// </summary>
        /// <returns>True if table was deleted successfully</returns>
        Task<bool> DeleteTableAsync();

        /// <summary>
        /// Checks if table exists for the model type.
        /// </summary>
        /// <returns>True if table exists</returns>
        Task<bool> TableExistsAsync();

        /// <summary>
        /// Validates that the existing table matches the model's configuration.
        /// Checks primary keys, secondary indexes, and their key schemas.
        /// </summary>
        /// <returns>Validation result with details about any mismatches</returns>
        Task<TableValidationResult> ValidateTableAsync();

        /// <summary>
        /// Gets the table name for the model type.
        /// </summary>
        /// <returns>Table name</returns>
        string GetTableName();

        /// <summary>
        /// Gets detailed information about the table schema expected by the model.
        /// Useful for documentation or debugging purposes.
        /// </summary>
        /// <returns>Table schema information</returns>
        Task<TableSchemaInfo> GetExpectedTableSchemaAsync();
    }

    /// <summary>
    /// Information about the expected table schema based on the model.
    /// </summary>
    public class TableSchemaInfo
    {
        /// <summary>
        /// Table name.
        /// </summary>
        public string TableName { get; set; } = string.Empty;

        /// <summary>
        /// Primary hash key attribute name.
        /// </summary>
        public string? HashKeyAttribute { get; set; }

        /// <summary>
        /// Primary range key attribute name.
        /// </summary>
        public string? RangeKeyAttribute { get; set; }

        /// <summary>
        /// List of Global Secondary Indexes expected.
        /// </summary>
        public List<SecondaryIndexInfo> GlobalSecondaryIndexes { get; set; } = new List<SecondaryIndexInfo>();

        /// <summary>
        /// List of Local Secondary Indexes expected.
        /// </summary>
        public List<SecondaryIndexInfo> LocalSecondaryIndexes { get; set; } = new List<SecondaryIndexInfo>();

        /// <summary>
        /// All attribute definitions required for the table.
        /// </summary>
        public List<AttributeInfo> AttributeDefinitions { get; set; } = new List<AttributeInfo>();
    }

    /// <summary>
    /// Information about a secondary index.
    /// </summary>
    public class SecondaryIndexInfo
    {
        /// <summary>
        /// Index name.
        /// </summary>
        public string IndexName { get; set; } = string.Empty;

        /// <summary>
        /// Index type (GSI or LSI).
        /// </summary>
        public string IndexType { get; set; } = string.Empty;

        /// <summary>
        /// Hash key attribute name for the index.
        /// </summary>
        public string HashKeyAttribute { get; set; } = string.Empty;

        /// <summary>
        /// Range key attribute name for the index (optional).
        /// </summary>
        public string? RangeKeyAttribute { get; set; }
    }

    /// <summary>
    /// Information about an attribute definition.
    /// </summary>
    public class AttributeInfo
    {
        /// <summary>
        /// Attribute name.
        /// </summary>
        public string AttributeName { get; set; } = string.Empty;

        /// <summary>
        /// Attribute type (S, N, B).
        /// </summary>
        public string AttributeType { get; set; } = string.Empty;
    }
}
