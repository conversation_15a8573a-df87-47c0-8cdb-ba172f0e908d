using Amazon.DynamoDBv2;
using Amazon.DynamoDBv2.DataModel;
using Microsoft.Extensions.DependencyInjection;
using shared.Models.Document;
using shared.Services;
using shared.Services.Implementation;

namespace shared.Extension
{
    /// <summary>
    /// Extension methods for configuring NoSQL repository services in dependency injection container.
    /// </summary>
    public static class NoSQLServiceExtensions
    {
        /// <summary>
        /// Adds DynamoDB repository services to the service collection.
        /// Registers the generic INoSQLRepository interface with DynamoDBRepository implementation.
        /// Note: This only registers the data repository. Use AddDynamoDBTableManager for table management.
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <returns>The service collection for chaining</returns>
        public static IServiceCollection AddDynamoDBRepository(this IServiceCollection services)
        {
            // Register the generic repository interface with DynamoDB implementation
            services.AddScoped(typeof(INoSQLRepository<>), typeof(DynamoDBRepository<>));

            return services;
        }

        /// <summary>
        /// Adds DynamoDB table manager services to the service collection.
        /// Registers the generic INoSQLTableManager interface with DynamoDBTableManager implementation.
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <returns>The service collection for chaining</returns>
        public static IServiceCollection AddDynamoDBTableManager(this IServiceCollection services)
        {
            // Register the generic table manager interface with DynamoDB implementation
            services.AddScoped(typeof(INoSQLTableManager<>), typeof(DynamoDBTableManager<>));

            return services;
        }

        /// <summary>
        /// Adds both DynamoDB repository and table manager services to the service collection.
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <returns>The service collection for chaining</returns>
        public static IServiceCollection AddDynamoDBServices(this IServiceCollection services)
        {
            services.AddDynamoDBRepository();
            services.AddDynamoDBTableManager();

            return services;
        }

        /// <summary>
        /// Adds DynamoDB repository services with custom configuration.
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <param name="configureOptions">Action to configure DynamoDB options</param>
        /// <returns>The service collection for chaining</returns>
        public static IServiceCollection AddDynamoDBRepository(this IServiceCollection services,
            Action<DynamoDBRepositoryOptions> configureOptions)
        {
            var options = new DynamoDBRepositoryOptions();
            configureOptions(options);

            services.AddSingleton(options);
            services.AddScoped(typeof(INoSQLRepository<>), typeof(DynamoDBRepository<>));

            return services;
        }

        /// <summary>
        /// Adds DynamoDB table manager services with custom configuration.
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <param name="configureOptions">Action to configure DynamoDB options</param>
        /// <returns>The service collection for chaining</returns>
        public static IServiceCollection AddDynamoDBTableManager(this IServiceCollection services,
            Action<DynamoDBRepositoryOptions> configureOptions)
        {
            var options = new DynamoDBRepositoryOptions();
            configureOptions(options);

            services.AddSingleton(options);
            services.AddScoped(typeof(INoSQLTableManager<>), typeof(DynamoDBTableManager<>));

            return services;
        }

        /// <summary>
        /// Adds both DynamoDB repository and table manager services with custom configuration.
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <param name="configureOptions">Action to configure DynamoDB options</param>
        /// <returns>The service collection for chaining</returns>
        public static IServiceCollection AddDynamoDBServices(this IServiceCollection services,
            Action<DynamoDBRepositoryOptions> configureOptions)
        {
            var options = new DynamoDBRepositoryOptions();
            configureOptions(options);

            services.AddSingleton(options);
            services.AddDynamoDBServices();

            return services;
        }

        /// <summary>
        /// Adds a specific DynamoDB repository for a particular model type.
        /// This allows for more granular control over repository registration.
        /// </summary>
        /// <typeparam name="T">The model type that extends DynamoDBModel</typeparam>
        /// <param name="services">The service collection</param>
        /// <returns>The service collection for chaining</returns>
        public static IServiceCollection AddDynamoDBRepository<T>(this IServiceCollection services)
            where T : DynamoDBModel, new()
        {
            services.AddScoped<INoSQLRepository<T>, DynamoDBRepository<T>>();
            return services;
        }

        /// <summary>
        /// Adds a specific DynamoDB table manager for a particular model type.
        /// This allows for more granular control over table manager registration.
        /// </summary>
        /// <typeparam name="T">The model type that extends DynamoDBModel</typeparam>
        /// <param name="services">The service collection</param>
        /// <returns>The service collection for chaining</returns>
        public static IServiceCollection AddDynamoDBTableManager<T>(this IServiceCollection services)
            where T : DynamoDBModel, new()
        {
            services.AddScoped<INoSQLTableManager<T>, DynamoDBTableManager<T>>();
            return services;
        }

        /// <summary>
        /// Adds both repository and table manager for a specific model type.
        /// </summary>
        /// <typeparam name="T">The model type that extends DynamoDBModel</typeparam>
        /// <param name="services">The service collection</param>
        /// <returns>The service collection for chaining</returns>
        public static IServiceCollection AddDynamoDBServices<T>(this IServiceCollection services)
            where T : DynamoDBModel, new()
        {
            services.AddDynamoDBRepository<T>();
            services.AddDynamoDBTableManager<T>();
            return services;
        }

        /// <summary>
        /// Adds all required AWS DynamoDB services along with the NoSQL repository and table manager.
        /// This is a convenience method that sets up the complete DynamoDB stack.
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <returns>The service collection for chaining</returns>
        public static IServiceCollection AddDynamoDBWithServices(this IServiceCollection services)
        {
            // Add AWS DynamoDB services
            services.AddAWSService<IAmazonDynamoDB>();
            services.AddSingleton<IDynamoDBContext, DynamoDBContext>();

            // Add both repository and table manager
            services.AddDynamoDBServices();

            return services;
        }

        /// <summary>
        /// Adds all required AWS DynamoDB services along with only the NoSQL repository.
        /// Use this when you only need data operations without table management.
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <returns>The service collection for chaining</returns>
        public static IServiceCollection AddDynamoDBWithRepository(this IServiceCollection services)
        {
            // Add AWS DynamoDB services (only IDynamoDBContext needed for repository)
            services.AddAWSService<IAmazonDynamoDB>();
            services.AddSingleton<IDynamoDBContext, DynamoDBContext>();

            // Add only the repository
            services.AddDynamoDBRepository();

            return services;
        }

        /// <summary>
        /// Adds all required AWS DynamoDB services along with only the table manager.
        /// Use this when you only need table management operations.
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <returns>The service collection for chaining</returns>
        public static IServiceCollection AddDynamoDBWithTableManager(this IServiceCollection services)
        {
            // Add AWS DynamoDB services (IAmazonDynamoDB needed for table manager)
            services.AddAWSService<IAmazonDynamoDB>();
            services.AddSingleton<IDynamoDBContext, DynamoDBContext>();

            // Add only the table manager
            services.AddDynamoDBTableManager();

            return services;
        }

        /// <summary>
        /// Adds all required AWS DynamoDB services along with both repository and table manager with custom configuration.
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <param name="configureOptions">Action to configure DynamoDB options</param>
        /// <returns>The service collection for chaining</returns>
        public static IServiceCollection AddDynamoDBWithServices(this IServiceCollection services,
            Action<DynamoDBRepositoryOptions> configureOptions)
        {
            // Add AWS DynamoDB services
            services.AddAWSService<IAmazonDynamoDB>();
            services.AddSingleton<IDynamoDBContext, DynamoDBContext>();

            // Add both repository and table manager with configuration
            services.AddDynamoDBServices(configureOptions);

            return services;
        }
    }

    /// <summary>
    /// Configuration options for DynamoDB repository.
    /// </summary>
    public class DynamoDBRepositoryOptions
    {
        /// <summary>
        /// Default table creation timeout in minutes.
        /// </summary>
        public int TableCreationTimeoutMinutes { get; set; } = 5;

        /// <summary>
        /// Whether to automatically create tables if they don't exist.
        /// </summary>
        public bool AutoCreateTables { get; set; } = false;

        /// <summary>
        /// Default billing mode for created tables.
        /// </summary>
        public string DefaultBillingMode { get; set; } = "PAY_PER_REQUEST";

        /// <summary>
        /// Whether to enable point-in-time recovery for created tables.
        /// </summary>
        public bool EnablePointInTimeRecovery { get; set; } = false;

        /// <summary>
        /// Default read capacity units for provisioned billing mode.
        /// </summary>
        public long DefaultReadCapacityUnits { get; set; } = 5;

        /// <summary>
        /// Default write capacity units for provisioned billing mode.
        /// </summary>
        public long DefaultWriteCapacityUnits { get; set; } = 5;
    }
}
