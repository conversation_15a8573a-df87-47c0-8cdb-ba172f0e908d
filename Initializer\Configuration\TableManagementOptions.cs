namespace Initializer.Configuration
{
    /// <summary>
    /// Configuration options for table management operations.
    /// </summary>
    public class TableManagementOptions
    {
        public const string SectionName = "TableManagement";

        /// <summary>
        /// Maximum time to wait for table operations to complete (in minutes).
        /// </summary>
        public int WaitTimeoutMinutes { get; set; } = 10;

        /// <summary>
        /// Whether to enable automatic table recreation when validation fails.
        /// </summary>
        public bool EnableTableRecreation { get; set; } = true;

        /// <summary>
        /// Whether to validate all tables on application startup.
        /// </summary>
        public bool ValidateTablesOnStartup { get; set; } = true;
    }
}
