using Microsoft.Extensions.Logging;
using Initializer.Services;
using System.Reflection;

namespace Initializer
{
    /// <summary>
    /// Main application class for table initialization operations.
    /// </summary>
    public class TableInitializerApp
    {
        private readonly IModelDiscoveryService _modelDiscovery;
        private readonly ITableValidationService _validationService;
        private readonly ITableSetupService _setupService;
        private readonly ILogger<TableInitializerApp> _logger;

        public TableInitializerApp(
            IModelDiscoveryService modelDiscovery,
            ITableValidationService validationService,
            ITableSetupService setupService,
            ILogger<TableInitializerApp> logger)
        {
            _modelDiscovery = modelDiscovery;
            _validationService = validationService;
            _setupService = setupService;
            _logger = logger;
        }

        /// <summary>
        /// Main entry point for the application.
        /// </summary>
        public async Task RunAsync(string[] args)
        {
            try
            {
                _logger.LogInformation("Starting Table Initializer Application");

                if (args.Length == 0)
                {
                    await ShowHelpAndRunInteractive();
                    return;
                }

                var command = args[0].ToLower();
                switch (command)
                {
                    case "discover":
                        await DiscoverModelsCommand();
                        break;
                    case "validate":
                        await ValidateTablesCommand(args.Skip(1).ToArray());
                        break;
                    case "setup":
                        await SetupTablesCommand(args.Skip(1).ToArray());
                        break;
                    case "recreate":
                        await RecreateTablesCommand(args.Skip(1).ToArray());
                        break;
                    case "help":
                    case "--help":
                    case "-h":
                        ShowHelp();
                        break;
                    default:
                        Console.WriteLine($"Unknown command: {command}");
                        ShowHelp();
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Application error occurred");
                Console.WriteLine($"Error: {ex.Message}");
                Environment.Exit(1);
            }
        }

        private async Task ShowHelpAndRunInteractive()
        {
            ShowHelp();
            Console.WriteLine("\nNo command specified. Running in interactive mode...\n");
            await RunInteractiveMode();
        }

        private void ShowHelp()
        {
            Console.WriteLine("Table Initializer - NoSQL Table Management Tool");
            Console.WriteLine("===============================================");
            Console.WriteLine();
            Console.WriteLine("Usage: Initializer <command> [options]");
            Console.WriteLine();
            Console.WriteLine("Commands:");
            Console.WriteLine("  discover                    - Discover all NoSQL model types");
            Console.WriteLine("  validate [namespace]        - Validate tables for all models or specific namespace");
            Console.WriteLine("  setup [namespace]           - Setup tables (validate and create/recreate as needed)");
            Console.WriteLine("  recreate [namespace]        - Force recreate all tables");
            Console.WriteLine("  help                        - Show this help message");
            Console.WriteLine();
            Console.WriteLine("Examples:");
            Console.WriteLine("  Initializer discover");
            Console.WriteLine("  Initializer validate");
            Console.WriteLine("  Initializer validate platform.Models");
            Console.WriteLine("  Initializer setup");
            Console.WriteLine("  Initializer setup shared.Models");
            Console.WriteLine("  Initializer recreate platform.Models.Document");
        }

        private async Task RunInteractiveMode()
        {
            while (true)
            {
                Console.WriteLine("\nSelect an option:");
                Console.WriteLine("1. Discover all models");
                Console.WriteLine("2. Validate all tables");
                Console.WriteLine("3. Setup all tables");
                Console.WriteLine("4. Recreate all tables");
                Console.WriteLine("5. Exit");
                Console.Write("\nEnter your choice (1-5): ");

                var choice = Console.ReadLine();
                switch (choice)
                {
                    case "1":
                        await DiscoverModelsCommand();
                        break;
                    case "2":
                        await ValidateTablesCommand(Array.Empty<string>());
                        break;
                    case "3":
                        await SetupTablesCommand(Array.Empty<string>());
                        break;
                    case "4":
                        Console.Write("Are you sure you want to recreate ALL tables? (yes/no): ");
                        var confirm = Console.ReadLine();
                        if (confirm?.ToLower() == "yes")
                        {
                            await RecreateTablesCommand(Array.Empty<string>());
                        }
                        break;
                    case "5":
                        return;
                    default:
                        Console.WriteLine("Invalid choice. Please enter 1-5.");
                        break;
                }
            }
        }

        private async Task DiscoverModelsCommand()
        {
            _logger.LogInformation("Discovering NoSQL model types");
            Console.WriteLine("Discovering NoSQL model types...\n");

            var models = _modelDiscovery.DiscoverAllModels().ToList();
            
            if (!models.Any())
            {
                Console.WriteLine("No NoSQL model types found.");
                return;
            }

            Console.WriteLine($"Found {models.Count} NoSQL model types:\n");

            var groupedByAssembly = models.GroupBy(m => m.Assembly.GetName().Name);
            foreach (var assemblyGroup in groupedByAssembly)
            {
                Console.WriteLine($"Assembly: {assemblyGroup.Key}");
                foreach (var model in assemblyGroup)
                {
                    Console.WriteLine($"  - {model.ModelType.FullName} -> Table: {model.TableName}");
                }
                Console.WriteLine();
            }
        }

        private async Task ValidateTablesCommand(string[] args)
        {
            var models = GetModelsForCommand(args);
            if (!models.Any())
            {
                Console.WriteLine("No models found to validate.");
                return;
            }

            _logger.LogInformation("Validating {ModelCount} tables", models.Count());
            Console.WriteLine($"Validating {models.Count()} tables...\n");

            var modelTypes = models.Select(m => m.ModelType).ToList();
            var results = await _validationService.ValidateTablesAsync(modelTypes);

            DisplayValidationResults(results);
        }

        private async Task SetupTablesCommand(string[] args)
        {
            var models = GetModelsForCommand(args);
            if (!models.Any())
            {
                Console.WriteLine("No models found to setup.");
                return;
            }

            _logger.LogInformation("Setting up {ModelCount} tables", models.Count());
            Console.WriteLine($"Setting up {models.Count()} tables...\n");

            var modelTypes = models.Select(m => m.ModelType).ToList();
            var results = await _setupService.SetupTablesAsync(modelTypes);

            DisplaySetupResults(results);
        }

        private async Task RecreateTablesCommand(string[] args)
        {
            var models = GetModelsForCommand(args);
            if (!models.Any())
            {
                Console.WriteLine("No models found to recreate.");
                return;
            }

            _logger.LogInformation("Recreating {ModelCount} tables", models.Count());
            Console.WriteLine($"Recreating {models.Count()} tables...\n");

            var modelTypes = models.Select(m => m.ModelType).ToList();
            var tasks = modelTypes.Select(async modelType =>
            {
                var result = await _setupService.RecreateTableAsync(modelType);
                return new KeyValuePair<Type, TableSetupResult>(modelType, result);
            });

            var results = await Task.WhenAll(tasks);
            var resultDict = results.ToDictionary(r => r.Key, r => r.Value);

            DisplaySetupResults(resultDict);
        }

        private IEnumerable<ModelInfo> GetModelsForCommand(string[] args)
        {
            try
            {
                var allModels = _modelDiscovery.DiscoverAllModels();

                if (args.Length == 0)
                {
                    return allModels;
                }

                var namespaceFilter = args[0];
                return _modelDiscovery.FilterModelsByNamespace(namespaceFilter);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error discovering models");
                Console.WriteLine($"Error discovering models: {ex.Message}");
                return Enumerable.Empty<ModelInfo>();
            }
        }

        private void DisplayValidationResults(Dictionary<Type, shared.Services.TableValidationResult> results)
        {
            var validCount = 0;
            var invalidCount = 0;

            foreach (var (modelType, result) in results)
            {
                var status = result.IsValid ? "✓ VALID" : "✗ INVALID";
                var color = result.IsValid ? ConsoleColor.Green : ConsoleColor.Red;
                
                Console.ForegroundColor = color;
                Console.WriteLine($"{status} - {modelType.Name}");
                Console.ResetColor();

                if (!result.IsValid)
                {
                    foreach (var error in result.ValidationErrors)
                    {
                        Console.WriteLine($"    Error: {error}");
                    }
                    invalidCount++;
                }
                else
                {
                    validCount++;
                }
                Console.WriteLine();
            }

            Console.WriteLine($"Summary: {validCount} valid, {invalidCount} invalid");
        }

        private void DisplaySetupResults(Dictionary<Type, TableSetupResult> results)
        {
            var successCount = 0;
            var failureCount = 0;

            foreach (var (modelType, result) in results)
            {
                var status = result.Success ? $"✓ {result.Action.ToUpper()}" : "✗ FAILED";
                var color = result.Success ? ConsoleColor.Green : ConsoleColor.Red;
                
                Console.ForegroundColor = color;
                Console.WriteLine($"{status} - {modelType.Name}");
                Console.ResetColor();

                if (!result.Success)
                {
                    foreach (var error in result.Errors)
                    {
                        Console.WriteLine($"    Error: {error}");
                    }
                    failureCount++;
                }
                else
                {
                    successCount++;
                }
                Console.WriteLine();
            }

            Console.WriteLine($"Summary: {successCount} successful, {failureCount} failed");
        }
    }
}
