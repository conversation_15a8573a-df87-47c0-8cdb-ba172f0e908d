using shared.Services;

namespace Initializer.Services
{
    /// <summary>
    /// Interface for table initialization operations.
    /// </summary>
    public interface ITableInitializationService
    {
        /// <summary>
        /// Validates and initializes tables for all discovered model types.
        /// </summary>
        /// <returns>Task representing the operation with success status</returns>
        Task<bool> InitializeAllTablesAsync();

        /// <summary>
        /// Validates and initializes tables for specific model types.
        /// </summary>
        /// <param name="modelTypes">Model types to process</param>
        /// <returns>Task representing the operation with success status</returns>
        Task<bool> InitializeTablesAsync(IEnumerable<Type> modelTypes);

        /// <summary>
        /// Validates and initializes a table for a specific model type.
        /// </summary>
        /// <param name="modelType">Model type to process</param>
        /// <returns>Task representing the operation with success status</returns>
        Task<bool> InitializeTableAsync(Type modelType);

        /// <summary>
        /// Validates a table for a specific model type.
        /// </summary>
        /// <param name="modelType">Model type to validate</param>
        /// <returns>Task representing the validation result</returns>
        Task<TableValidationResult> ValidateTableAsync(Type modelType);
    }
}
