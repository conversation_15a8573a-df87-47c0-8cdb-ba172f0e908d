using Amazon.DynamoDBv2.DataModel;
using platform.Models.Enum;
using shared.Converters;
using shared.Models.Document;
using System.Text.Json.Serialization;

namespace platform.Models.Document
{
    [DynamoDBTable("KnowledgeBaseFile")]
    public class KnowledgeBaseFile : BaseModel
    {
        public const string AccountIdFileIdIndexName = "AccountId-FileId-index";

        [DynamoDBHashKey]
        public string KbId { get; set; } = string.Empty;

        [DynamoDBRangeKey]
        [DynamoDBGlobalSecondaryIndexRangeKey(AccountIdFileIdIndexName)]
        public string FileId { get; set; } = string.Empty;

        [DynamoDBGlobalSecondaryIndexHashKey(AccountIdFileIdIndexName)]
        public string AccountId { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
        public long UploadedFileSize { get; set; }
        public bool IsDeployed { get; set; } = false;

        [DynamoDBProperty(typeof(DynamoEnumStringConverter<KnowledgeBaseFileUploadStatus>))]
        [JsonConverter(typeof(JsonEnumStringConverter<KnowledgeBaseFileUploadStatus>))]
        public KnowledgeBaseFileUploadStatus Status { get; set; }
    }
}
