using shared.Models.Document;
using shared.Models.Response;

namespace shared.Services
{
    /// <summary>
    /// Result of table validation operation.
    /// </summary>
    public class TableValidationResult
    {
        /// <summary>
        /// Whether the table is valid and matches the model configuration.
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// List of validation errors or mismatches found.
        /// </summary>
        public List<string> ValidationErrors { get; set; } = new List<string>();

        /// <summary>
        /// Whether the table exists.
        /// </summary>
        public bool TableExists { get; set; }

        /// <summary>
        /// Current table status if it exists.
        /// </summary>
        public string? TableStatus { get; set; }

        /// <summary>
        /// Details about primary key validation.
        /// </summary>
        public KeyValidationResult PrimaryKey { get; set; } = new KeyValidationResult();

        /// <summary>
        /// Details about secondary index validation.
        /// </summary>
        public List<IndexValidationResult> SecondaryIndexes { get; set; } = new List<IndexValidationResult>();
    }

    /// <summary>
    /// Result of key validation (primary or secondary index).
    /// </summary>
    public class KeyValidationResult
    {
        /// <summary>
        /// Whether the key configuration is valid.
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// Expected hash key attribute name.
        /// </summary>
        public string? ExpectedHashKey { get; set; }

        /// <summary>
        /// Actual hash key attribute name found in table.
        /// </summary>
        public string? ActualHashKey { get; set; }

        /// <summary>
        /// Expected range key attribute name.
        /// </summary>
        public string? ExpectedRangeKey { get; set; }

        /// <summary>
        /// Actual range key attribute name found in table.
        /// </summary>
        public string? ActualRangeKey { get; set; }

        /// <summary>
        /// List of validation errors for this key.
        /// </summary>
        public List<string> ValidationErrors { get; set; } = new List<string>();
    }

    /// <summary>
    /// Result of secondary index validation.
    /// </summary>
    public class IndexValidationResult : KeyValidationResult
    {
        /// <summary>
        /// Name of the secondary index.
        /// </summary>
        public string IndexName { get; set; } = string.Empty;

        /// <summary>
        /// Type of index (GSI or LSI).
        /// </summary>
        public string IndexType { get; set; } = string.Empty;

        /// <summary>
        /// Whether the index exists in the table.
        /// </summary>
        public bool IndexExists { get; set; }

        /// <summary>
        /// Current index status if it exists.
        /// </summary>
        public string? IndexStatus { get; set; }
    }
}
{
    /// <summary>
    /// Generic NoSQL repository interface that provides abstraction for NoSQL database operations.
    /// Supports basic CRUD operations, search, and secondary index queries.
    /// Note: Table management operations are handled by INoSQLTableManager.
    /// </summary>
    /// <typeparam name="T">Model type that extends NoSQLModel</typeparam>
    public interface INoSQLRepository<T> where T : NoSQLModel
    {

        /// <summary>
        /// Gets an item by hash key only (for tables with only hash key).
        /// </summary>
        /// <param name="hashKey">Hash key value</param>
        /// <returns>Model instance or null if not found</returns>
        Task<T?> GetAsync(string hashKey);

        /// <summary>
        /// Gets an item by hash key and range key.
        /// </summary>
        /// <param name="hashKey">Hash key value</param>
        /// <param name="rangeKey">Range key value</param>
        /// <returns>Model instance or null if not found</returns>
        Task<T?> GetAsync(string hashKey, string rangeKey);

        /// <summary>
        /// Gets an item using a secondary index.
        /// </summary>
        /// <param name="hashKey">Hash key value for the index</param>
        /// <param name="rangeKey">Range key value for the index</param>
        /// <param name="indexName">Name of the secondary index</param>
        /// <returns>Model instance or null if not found</returns>
        Task<T?> GetByIndexAsync(string hashKey, string rangeKey, string indexName);

        /// <summary>
        /// Gets an item using a secondary index with only hash key.
        /// </summary>
        /// <param name="hashKey">Hash key value for the index</param>
        /// <param name="indexName">Name of the secondary index</param>
        /// <returns>Model instance or null if not found</returns>
        Task<T?> GetByIndexAsync(string hashKey, string indexName);

        /// <summary>
        /// Inserts or updates an item.
        /// </summary>
        /// <param name="item">Item to insert/update</param>
        /// <returns>Updated item with new version and timestamp</returns>
        Task<T?> PutAsync(T item);

        /// <summary>
        /// Updates specific fields of an item atomically.
        /// </summary>
        /// <param name="item">Item with updated values</param>
        /// <param name="fields">List of field names to update</param>
        /// <param name="atomic">Whether to use atomic update with version check</param>
        /// <returns>Updated item or null if update failed</returns>
        Task<T?> UpdateAsync(T item, List<string> fields, bool atomic = true);

        /// <summary>
        /// Deletes an item by hash key only.
        /// </summary>
        /// <param name="hashKey">Hash key value</param>
        /// <returns>True if deletion was successful</returns>
        Task<bool> DeleteAsync(string hashKey);

        /// <summary>
        /// Deletes an item by hash key and range key.
        /// </summary>
        /// <param name="hashKey">Hash key value</param>
        /// <param name="rangeKey">Range key value</param>
        /// <returns>True if deletion was successful</returns>
        Task<bool> DeleteAsync(string hashKey, string rangeKey);

        /// <summary>
        /// Deletes an item using a secondary index.
        /// </summary>
        /// <param name="hashKey">Hash key value for the index</param>
        /// <param name="rangeKey">Range key value for the index</param>
        /// <param name="indexName">Name of the secondary index</param>
        /// <returns>True if deletion was successful</returns>
        Task<bool> DeleteByIndexAsync(string hashKey, string rangeKey, string indexName);

        /// <summary>
        /// Searches items with text search on SearchString field.
        /// </summary>
        /// <param name="hashKey">Hash key value</param>
        /// <param name="searchText">Text to search for</param>
        /// <param name="maxItems">Maximum number of items to return</param>
        /// <param name="paginationToken">Token for pagination</param>
        /// <param name="attributesToGet">Specific attributes to retrieve</param>
        /// <param name="getTotal">Whether to get total count</param>
        /// <returns>List response with items and pagination info</returns>
        Task<ListResponse<T>> SearchAsync(string hashKey, string searchText, int maxItems, 
            string? paginationToken = null, List<string>? attributesToGet = null, bool getTotal = true);

        /// <summary>
        /// Searches items using a secondary index with text search.
        /// </summary>
        /// <param name="hashKey">Hash key value for the index</param>
        /// <param name="searchText">Text to search for</param>
        /// <param name="indexName">Name of the secondary index</param>
        /// <param name="maxItems">Maximum number of items to return</param>
        /// <param name="paginationToken">Token for pagination</param>
        /// <param name="attributesToGet">Specific attributes to retrieve</param>
        /// <param name="getTotal">Whether to get total count</param>
        /// <returns>List response with items and pagination info</returns>
        Task<ListResponse<T>> SearchByIndexAsync(string hashKey, string searchText, string indexName, 
            int maxItems, string? paginationToken = null, List<string>? attributesToGet = null, bool getTotal = true);

        /// <summary>
        /// Queries items by hash key with optional range key conditions.
        /// </summary>
        /// <param name="hashKey">Hash key value</param>
        /// <param name="maxItems">Maximum number of items to return</param>
        /// <param name="paginationToken">Token for pagination</param>
        /// <param name="attributesToGet">Specific attributes to retrieve</param>
        /// <param name="getTotal">Whether to get total count</param>
        /// <returns>List response with items and pagination info</returns>
        Task<ListResponse<T>> QueryAsync(string hashKey, int maxItems, 
            string? paginationToken = null, List<string>? attributesToGet = null, bool getTotal = true);

        /// <summary>
        /// Queries items using a secondary index.
        /// </summary>
        /// <param name="hashKey">Hash key value for the index</param>
        /// <param name="indexName">Name of the secondary index</param>
        /// <param name="maxItems">Maximum number of items to return</param>
        /// <param name="paginationToken">Token for pagination</param>
        /// <param name="attributesToGet">Specific attributes to retrieve</param>
        /// <param name="getTotal">Whether to get total count</param>
        /// <returns>List response with items and pagination info</returns>
        Task<ListResponse<T>> QueryByIndexAsync(string hashKey, string indexName, int maxItems, 
            string? paginationToken = null, List<string>? attributesToGet = null, bool getTotal = true);

        /// <summary>
        /// Batch gets multiple items by their keys.
        /// </summary>
        /// <param name="hashKey">Hash key value</param>
        /// <param name="rangeKeys">List of range key values</param>
        /// <returns>List of found items</returns>
        Task<List<T>> BatchGetAsync(string hashKey, List<string> rangeKeys);

        /// <summary>
        /// Batch gets multiple items using a secondary index.
        /// </summary>
        /// <param name="hashKey">Hash key value for the index</param>
        /// <param name="rangeKeys">List of range key values for the index</param>
        /// <param name="indexName">Name of the secondary index</param>
        /// <returns>List of found items</returns>
        Task<List<T>> BatchGetByIndexAsync(string hashKey, List<string> rangeKeys, string indexName);
    }
}
