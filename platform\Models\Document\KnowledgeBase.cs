using Amazon.DynamoDBv2.DataModel;
using platform.Models.Enum;
using shared.Converters;
using shared.Models.Document;
using System.ComponentModel;
using System.Text.Json.Serialization;

namespace platform.Models.Document
{

    [DynamoDBTable("KnowledgeBase")]
    public class KnowledgeBase : BaseModel
    {
        [DynamoDBHashKey]
        public string AccountId { get; set; } = string.Empty;

        [DynamoDBRangeKey]
        public string KbId { get; set; } = string.Empty;

        [DynamoDBProperty(typeof(DynamoEnumStringConverter<KnowledgeBaseStatus>))]
        [JsonConverter(typeof(JsonEnumStringConverter<KnowledgeBaseStatus>))]
        public KnowledgeBaseStatus Status { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Tag {  get; set; } = string.Empty;

        public override string GetSearchString()
        {
            return Name.ToLower();
        }
    }
}
