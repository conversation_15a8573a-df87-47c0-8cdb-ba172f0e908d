﻿using Amazon.DynamoDBv2.DataModel;
using System.Reflection;

namespace shared.Models.Document
{
    /// <summary>
    /// Base model class that extends DynamoDBModel to provide backward compatibility.
    /// This class maintains the existing API while leveraging the new NoSQL abstraction layer.
    /// </summary>
    public class BaseModel : DynamoDBModel
    {
        /// <summary>
        /// Gets the hash key property name for the model type.
        /// Overrides the base implementation to provide static polymorphism.
        /// </summary>
        /// <param name="indexName">Optional index name for secondary indexes</param>
        /// <returns>Hash key property name</returns>
        public static new string? GetHashKeyPropertyName(string? indexName = null)
        {
            return DynamoDBModel.GetHashKeyPropertyName(typeof(BaseModel), indexName);
        }

        /// <summary>
        /// Gets the range key property name for the model type.
        /// Overrides the base implementation to provide static polymorphism.
        /// </summary>
        /// <param name="indexName">Optional index name for secondary indexes</param>
        /// <returns>Range key property name</returns>
        public static new string? GetRangeKeyPropertyName(string? indexName = null)
        {
            return DynamoDBModel.GetRangeKeyPropertyName(typeof(BaseModel), indexName);
        }

        /// <summary>
        /// Gets the table name for the model type.
        /// Overrides the base implementation to provide static polymorphism.
        /// </summary>
        /// <returns>Table name</returns>
        public static new string GetTableName()
        {
            return DynamoDBModel.GetTableName(typeof(BaseModel));
        }

        /// <summary>
        /// Backward compatibility method for getting hash key value.
        /// Maps to the new GetHashKeyValue method.
        /// </summary>
        /// <returns>Hash key value</returns>
        public virtual string? GetHashKey()
        {
            return GetHashKeyValue();
        }

        /// <summary>
        /// Backward compatibility method for getting range key value.
        /// Maps to the new GetRangeKeyValue method.
        /// </summary>
        /// <returns>Range key value</returns>
        public virtual string? GetRangeKey()
        {
            return GetRangeKeyValue();
        }

        /// <summary>
        /// Backward compatibility method for getting property by attribute.
        /// Maintains the existing API signature.
        /// </summary>
        /// <param name="T">Type to search</param>
        /// <param name="attributeType">Attribute type to find</param>
        /// <returns>Property info</returns>
        public static PropertyInfo? GetPropertyByAttribute(Type T, Type attributeType)
        {
            return NoSQLModel.GetPropertyByAttribute(T, attributeType);
        }
    }
}
