using Amazon.DynamoDBv2.DataModel;
using Microsoft.Extensions.Logging;
using shared.Models.Document;
using System.Reflection;

namespace Initializer.Services
{
    /// <summary>
    /// Service for discovering NoSQL model types from assemblies using reflection.
    /// </summary>
    public class ModelTypeDiscoveryService : IModelTypeDiscoveryService
    {
        private readonly ILogger<ModelTypeDiscoveryService> _logger;

        public ModelTypeDiscoveryService(ILogger<ModelTypeDiscoveryService> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// Discovers all NoSQL model types that inherit from DynamoDBModel.
        /// </summary>
        /// <returns>Collection of model types</returns>
        public IEnumerable<Type> DiscoverModelTypes()
        {
            _logger.LogInformation("Starting discovery of NoSQL model types from all loaded assemblies");

            var modelTypes = new List<Type>();
            var assemblies = GetModelAssemblies();

            foreach (var assembly in assemblies)
            {
                try
                {
                    var types = assembly.GetTypes()
                        .Where(IsValidModelType)
                        .ToList();

                    modelTypes.AddRange(types);
                    
                    if (types.Any())
                    {
                        _logger.LogInformation("Found {Count} model types in assembly {AssemblyName}: {Types}",
                            types.Count, 
                            assembly.GetName().Name,
                            string.Join(", ", types.Select(t => t.Name)));
                    }
                }
                catch (ReflectionTypeLoadException ex)
                {
                    _logger.LogWarning("Failed to load types from assembly {AssemblyName}: {Message}",
                        assembly.GetName().Name, ex.Message);
                    
                    // Try to get the types that were successfully loaded
                    var loadedTypes = ex.Types.Where(t => t != null && IsValidModelType(t!)).ToList();
                    if (loadedTypes.Any())
                    {
                        modelTypes.AddRange(loadedTypes!);
                        _logger.LogInformation("Recovered {Count} model types from partially loaded assembly {AssemblyName}",
                            loadedTypes.Count, assembly.GetName().Name);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing assembly {AssemblyName}", assembly.GetName().Name);
                }
            }

            _logger.LogInformation("Discovery completed. Found {TotalCount} total model types", modelTypes.Count);
            return modelTypes.Distinct();
        }

        /// <summary>
        /// Discovers NoSQL model types from specific assemblies.
        /// </summary>
        /// <param name="assemblyNames">Names of assemblies to search</param>
        /// <returns>Collection of model types</returns>
        public IEnumerable<Type> DiscoverModelTypes(params string[] assemblyNames)
        {
            _logger.LogInformation("Starting discovery of NoSQL model types from specific assemblies: {Assemblies}",
                string.Join(", ", assemblyNames));

            var modelTypes = new List<Type>();

            foreach (var assemblyName in assemblyNames)
            {
                try
                {
                    var assembly = Assembly.LoadFrom(assemblyName);
                    var types = assembly.GetTypes()
                        .Where(IsValidModelType)
                        .ToList();

                    modelTypes.AddRange(types);
                    
                    _logger.LogInformation("Found {Count} model types in assembly {AssemblyName}: {Types}",
                        types.Count, 
                        assemblyName,
                        string.Join(", ", types.Select(t => t.Name)));
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error loading assembly {AssemblyName}", assemblyName);
                }
            }

            _logger.LogInformation("Discovery completed. Found {TotalCount} total model types from specified assemblies", modelTypes.Count);
            return modelTypes.Distinct();
        }

        /// <summary>
        /// Gets all loaded assemblies that contain NoSQL models.
        /// </summary>
        /// <returns>Collection of assemblies</returns>
        public IEnumerable<Assembly> GetModelAssemblies()
        {
            var assemblies = AppDomain.CurrentDomain.GetAssemblies()
                .Where(assembly => 
                {
                    var name = assembly.GetName().Name;
                    return name != null && (
                        name.Contains("shared", StringComparison.OrdinalIgnoreCase) ||
                        name.Contains("platform", StringComparison.OrdinalIgnoreCase) ||
                        name.Contains("coral-agents", StringComparison.OrdinalIgnoreCase) ||
                        name.Contains("Models", StringComparison.OrdinalIgnoreCase)
                    );
                })
                .ToList();

            _logger.LogDebug("Found {Count} assemblies to search for models: {Assemblies}",
                assemblies.Count,
                string.Join(", ", assemblies.Select(a => a.GetName().Name)));

            return assemblies;
        }

        /// <summary>
        /// Determines if a type is a valid NoSQL model type.
        /// </summary>
        /// <param name="type">Type to check</param>
        /// <returns>True if the type is a valid model type</returns>
        private static bool IsValidModelType(Type type)
        {
            return type.IsClass &&
                   !type.IsAbstract &&
                   typeof(DynamoDBModel).IsAssignableFrom(type) &&
                   type.GetCustomAttribute<DynamoDBTableAttribute>() != null;
        }
    }
}
