using Amazon.DynamoDBv2.DataModel;
using shared.Models.Response;

namespace platform.Models.Response
{
    public class AgentAliasResponse : BaseModelResponse
    {
        public string Alias { get; set; } = string.Empty;
        public string AgentId { get; set; } = string.Empty;
        public string AgentName { get; set; } = string.Empty;
        public string AgentTag { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
    }
}
